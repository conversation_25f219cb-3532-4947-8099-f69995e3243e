#!/usr/bin/env python3
"""
MAVLink桥接测试脚本
用于测试和验证MAVLink到ROS2的数据转换
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped, TwistStamped
from nav_msgs.msg import Odometry
from geographic_msgs.msg import GeoPoint
from std_srvs.srv import Empty


class MavlinkBridgeTest(Node):
    """MAVLink桥接测试节点"""
    
    def __init__(self):
        super().__init__('mavlink_bridge_test')
        
        # 创建订阅者
        self.gps_sub = self.create_subscription(
            NavSatFix, 
            '/mavlink/gps/fix', 
            self.gps_callback, 
            10
        )
        
        self.pose_sub = self.create_subscription(
            PoseStamped,
            '/mavlink/pose',
            self.pose_callback,
            10
        )
        
        self.odom_sub = self.create_subscription(
            Odometry,
            '/mavlink/odom',
            self.odom_callback,
            10
        )
        
        self.velocity_sub = self.create_subscription(
            TwistStamped,
            '/mavlink/velocity',
            self.velocity_callback,
            10
        )
        
        self.waypoint_sub = self.create_subscription(
            GeoPoint,
            '/mavlink/current_waypoint',
            self.waypoint_callback,
            10
        )
        
        # 创建服务客户端
        self.request_waypoints_client = self.create_client(
            Empty, 
            '/mavlink/request_waypoints'
        )
        
        # 数据计数器
        self.gps_count = 0
        self.pose_count = 0
        self.odom_count = 0
        self.velocity_count = 0
        self.waypoint_count = 0
        
        # 创建定时器来显示统计信息
        self.timer = self.create_timer(5.0, self.print_statistics)
        
        self.get_logger().info('MAVLink桥接测试节点已启动')
        self.get_logger().info('等待MAVLink数据...')
    
    def gps_callback(self, msg):
        """GPS数据回调"""
        self.gps_count += 1
        if self.gps_count % 10 == 1:  # 每10条消息打印一次
            self.get_logger().info(
                f'GPS: 纬度={msg.latitude:.6f}, '
                f'经度={msg.longitude:.6f}, '
                f'高度={msg.altitude:.2f}m, '
                f'状态={msg.status.status}'
            )
    
    def pose_callback(self, msg):
        """姿态数据回调"""
        self.pose_count += 1
        if self.pose_count % 10 == 1:
            q = msg.pose.orientation
            self.get_logger().info(
                f'姿态: x={q.x:.3f}, y={q.y:.3f}, z={q.z:.3f}, w={q.w:.3f}'
            )
    
    def odom_callback(self, msg):
        """里程计数据回调"""
        self.odom_count += 1
        if self.odom_count % 10 == 1:
            pos = msg.pose.pose.position
            vel = msg.twist.twist.linear
            self.get_logger().info(
                f'里程计: 位置=({pos.x:.2f}, {pos.y:.2f}, {pos.z:.2f}), '
                f'速度=({vel.x:.2f}, {vel.y:.2f}, {vel.z:.2f})'
            )
    
    def velocity_callback(self, msg):
        """速度数据回调"""
        self.velocity_count += 1
        if self.velocity_count % 10 == 1:
            lin = msg.twist.linear
            ang = msg.twist.angular
            self.get_logger().info(
                f'速度: 线速度=({lin.x:.2f}, {lin.y:.2f}, {lin.z:.2f}), '
                f'角速度=({ang.x:.2f}, {ang.y:.2f}, {ang.z:.2f})'
            )
    
    def waypoint_callback(self, msg):
        """航点数据回调"""
        self.waypoint_count += 1
        self.get_logger().info(
            f'当前航点: 纬度={msg.latitude:.6f}, '
            f'经度={msg.longitude:.6f}, '
            f'高度={msg.altitude:.2f}m'
        )
    
    def print_statistics(self):
        """打印统计信息"""
        self.get_logger().info(
            f'数据统计 - GPS: {self.gps_count}, '
            f'姿态: {self.pose_count}, '
            f'里程计: {self.odom_count}, '
            f'速度: {self.velocity_count}, '
            f'航点: {self.waypoint_count}'
        )
    
    def request_waypoints(self):
        """请求航点"""
        if not self.request_waypoints_client.wait_for_service(timeout_sec=1.0):
            self.get_logger().warn('航点服务不可用')
            return
            
        request = Empty.Request()
        future = self.request_waypoints_client.call_async(request)
        future.add_done_callback(self.waypoint_request_callback)
    
    def waypoint_request_callback(self, future):
        """航点请求回调"""
        try:
            response = future.result()
            self.get_logger().info('航点请求已发送')
        except Exception as e:
            self.get_logger().error(f'航点请求失败: {e}')


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    try:
        test_node = MavlinkBridgeTest()
        
        # 等待一段时间后请求航点
        import threading
        def delayed_waypoint_request():
            import time
            time.sleep(10)  # 等待10秒
            test_node.request_waypoints()
        
        waypoint_thread = threading.Thread(target=delayed_waypoint_request, daemon=True)
        waypoint_thread.start()
        
        rclpy.spin(test_node)
        
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'测试节点运行错误: {e}')
    finally:
        if 'test_node' in locals():
            test_node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
