#!/usr/bin/env python3
"""
MAVLink桥接启动文件
"""

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    """生成启动描述"""
    
    # 声明启动参数
    connection_string_arg = DeclareLaunchArgument(
        'connection_string',
        default_value='/dev/ttyUSB0',
        description='MAVLink连接字符串 (串口/UDP/TCP)'
    )
    
    baud_rate_arg = DeclareLaunchArgument(
        'baud_rate',
        default_value='57600',
        description='串口波特率'
    )
    
    frame_id_arg = DeclareLaunchArgument(
        'frame_id',
        default_value='base_link',
        description='机体坐标系ID'
    )
    
    gps_frame_id_arg = DeclareLaunchArgument(
        'gps_frame_id',
        default_value='gps',
        description='GPS坐标系ID'
    )
    
    # MAVLink桥接节点
    mavlink_bridge_node = Node(
        package='pymavlink_bridge',
        executable='mavlink_bridge',
        name='mavlink_bridge',
        output='screen',
        parameters=[{
            'connection_string': LaunchConfiguration('connection_string'),
            'baud_rate': LaunchConfiguration('baud_rate'),
            'frame_id': LaunchConfiguration('frame_id'),
            'gps_frame_id': LaunchConfiguration('gps_frame_id'),
        }],
        remappings=[
            ('gps/fix', 'mavlink/gps/fix'),
            ('pose', 'mavlink/pose'),
            ('odom', 'mavlink/odom'),
            ('velocity', 'mavlink/velocity'),
        ]
    )
    
    # 航点服务节点
    waypoint_service_node = Node(
        package='pymavlink_bridge',
        executable='waypoint_service',
        name='waypoint_service',
        output='screen',
        parameters=[{
            'connection_string': LaunchConfiguration('connection_string'),
            'baud_rate': LaunchConfiguration('baud_rate'),
        }],
        remappings=[
            ('current_waypoint', 'mavlink/current_waypoint'),
            ('request_waypoints', 'mavlink/request_waypoints'),
            ('get_waypoints', 'mavlink/get_waypoints'),
        ]
    )
    
    return LaunchDescription([
        connection_string_arg,
        baud_rate_arg,
        frame_id_arg,
        gps_frame_id_arg,
        mavlink_bridge_node,
        waypoint_service_node,
    ])
