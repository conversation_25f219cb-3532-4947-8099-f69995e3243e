from setuptools import find_packages, setup

package_name = 'pymavlink_bridge'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/mavlink_bridge.launch.py']),
    ],
    install_requires=['setuptools', 'pymavlink'],
    zip_safe=True,
    maintainer='ubuntu',
    maintainer_email='<EMAIL>',
    description='轻量级PyMAVLink到ROS2桥接包',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'mavlink_bridge = pymavlink_bridge.mavlink_bridge:main',
            'waypoint_service = pymavlink_bridge.waypoint_service:main',
        ],
    },
)
