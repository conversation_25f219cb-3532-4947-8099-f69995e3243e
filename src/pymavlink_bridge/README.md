# PyMAVLink ROS2 桥接包

这是一个轻量级的PyMAVLink到ROS2桥接包，专门为ArduPilot飞控系统设计，提供GPS、姿态、速度和航点信息的转换。

## 功能特性

### 数据转换
- **GPS信息**: `GPS_RAW_INT` / `GLOBAL_POSITION_INT` → `sensor_msgs/NavSatFix`
- **姿态信息**: `ATTITUDE` → `geometry_msgs/PoseStamped` 和 `nav_msgs/Odometry`
- **速度信息**: `VFR_HUD` / `LOCAL_POSITION_NED` → `geometry_msgs/TwistStamped`
- **航点信息**: MAVLink任务协议 → ROS2服务

### 发布的话题
- `/mavlink/gps/fix` (`sensor_msgs/NavSatFix`) - GPS定位信息
- `/mavlink/pose` (`geometry_msgs/PoseStamped`) - 姿态信息
- `/mavlink/odom` (`nav_msgs/Odometry`) - 里程计信息
- `/mavlink/velocity` (`geometry_msgs/TwistStamped`) - 速度信息
- `/mavlink/current_waypoint` (`geographic_msgs/GeoPoint`) - 当前航点

### 提供的服务
- `/mavlink/request_waypoints` (`std_srvs/Empty`) - 请求航点列表
- `/mavlink/get_waypoints` (`geometry_msgs/GetPlan`) - 获取航点路径

## 安装依赖

```bash
# 安装PyMAVLink
pip3 install pymavlink

# 安装ROS2依赖 (如果还没有安装)
sudo apt install ros-humble-geographic-msgs
```

## 编译

```bash
cd ~/pymavlink_ros2
colcon build --packages-select pymavlink_bridge
source install/setup.bash
```

## 使用方法

### 1. 基本启动

```bash
# 启动桥接节点 (默认使用/dev/ttyUSB0)
ros2 launch pymavlink_bridge mavlink_bridge.launch.py

# 或指定连接参数
ros2 launch pymavlink_bridge mavlink_bridge.launch.py \
    connection_string:=/dev/ttyACM0 \
    baud_rate:=115200
```

### 2. SITL仿真连接

```bash
# 连接到ArduPilot SITL
ros2 launch pymavlink_bridge mavlink_bridge.launch.py \
    connection_string:=udp:127.0.0.1:14550
```

### 3. 单独启动节点

```bash
# 只启动数据桥接节点
ros2 run pymavlink_bridge mavlink_bridge

# 只启动航点服务节点
ros2 run pymavlink_bridge waypoint_service
```

### 4. 查看数据

```bash
# 查看GPS数据
ros2 topic echo /mavlink/gps/fix

# 查看姿态数据
ros2 topic echo /mavlink/pose

# 查看速度数据
ros2 topic echo /mavlink/velocity

# 查看里程计数据
ros2 topic echo /mavlink/odom
```

### 5. 航点操作

```bash
# 请求航点列表
ros2 service call /mavlink/request_waypoints std_srvs/srv/Empty

# 获取航点路径
ros2 service call /mavlink/get_waypoints geometry_msgs/srv/GetPlan
```

## 配置参数

主要参数可以在 `config/mavlink_params.yaml` 中配置：

- `connection_string`: MAVLink连接字符串
- `baud_rate`: 串口波特率
- `frame_id`: 机体坐标系ID
- `gps_frame_id`: GPS坐标系ID

## 连接方式

### 串口连接
```
connection_string: "/dev/ttyUSB0"
baud_rate: 57600
```

### UDP连接 (SITL)
```
connection_string: "udp:127.0.0.1:14550"
```

### TCP连接
```
connection_string: "tcp:127.0.0.1:5760"
```

## 坐标系说明

- **GPS数据**: WGS84坐标系
- **姿态数据**: 机体坐标系 (base_link)
- **速度数据**: NED坐标系 (北-东-下)

## 注意事项

1. 确保飞控已正确连接并配置
2. 检查串口权限: `sudo usermod -a -G dialout $USER`
3. 对于SITL，确保ArduPilot仿真器正在运行
4. 航点功能需要飞控支持MAVLink任务协议

## 故障排除

### 连接问题
- 检查设备路径是否正确
- 确认波特率设置
- 检查串口权限

### 数据问题
- 确认飞控正在发送相应的MAVLink消息
- 检查消息频率设置
- 使用MAVLink调试工具验证数据流

## 扩展开发

这个包设计为轻量级和可扩展的。你可以：

1. 添加更多MAVLink消息类型的支持
2. 实现自定义的坐标转换
3. 添加更多ROS2服务接口
4. 集成到Nav2导航栈中
