#!/usr/bin/env python3
"""
航点服务节点 - 提供航点相关的ROS2服务
"""

import rclpy
from rclpy.node import Node
from pymavlink import mavutil

from std_msgs.msg import Header
from geometry_msgs.msg import Point
from geographic_msgs.msg import GeoPoint
from std_srvs.srv import Empty
from geometry_msgs.srv import GetPlan


class WaypointService(Node):
    """航点服务节点"""
    
    def __init__(self):
        super().__init__('waypoint_service')
        
        # 声明参数
        self.declare_parameter('connection_string', '/dev/ttyUSB0')
        self.declare_parameter('baud_rate', 57600)
        
        # 获取参数
        self.connection_string = self.get_parameter('connection_string').value
        self.baud_rate = self.get_parameter('baud_rate').value
        
        # 初始化MAVLink连接
        self.mavlink_connection = None
        self.connect_to_mavlink()
        
        # 航点数据存储
        self.waypoints = []
        self.current_waypoint_index = 0
        
        # 创建服务
        self.request_waypoints_srv = self.create_service(
            Empty, 
            'request_waypoints', 
            self.request_waypoints_callback
        )
        
        self.get_waypoints_srv = self.create_service(
            GetPlan,
            'get_waypoints',
            self.get_waypoints_callback
        )
        
        # 创建发布者用于发布航点信息
        self.waypoint_pub = self.create_publisher(GeoPoint, 'current_waypoint', 10)
        
        self.get_logger().info('航点服务节点已启动')
    
    def connect_to_mavlink(self):
        """连接到MAVLink"""
        try:
            self.mavlink_connection = mavutil.mavlink_connection(
                self.connection_string, 
                baud=self.baud_rate
            )
            self.get_logger().info('MAVLink连接成功')
            return True
        except Exception as e:
            self.get_logger().error(f'MAVLink连接失败: {e}')
            return False
    
    def request_waypoints_callback(self, request, response):
        """请求航点列表的服务回调"""
        if not self.mavlink_connection:
            self.get_logger().error('MAVLink未连接')
            return response
            
        try:
            # 清空现有航点
            self.waypoints.clear()
            
            # 请求任务列表
            self.mavlink_connection.mav.mission_request_list_send(
                self.mavlink_connection.target_system,
                self.mavlink_connection.target_component
            )
            
            # 等待并接收航点数据
            self.receive_waypoints()
            
            self.get_logger().info(f'成功获取 {len(self.waypoints)} 个航点')
            
        except Exception as e:
            self.get_logger().error(f'请求航点失败: {e}')
            
        return response
    
    def receive_waypoints(self, timeout=5.0):
        """接收航点数据"""
        import time
        start_time = time.time()
        waypoint_count = None
        received_waypoints = 0
        
        while time.time() - start_time < timeout:
            msg = self.mavlink_connection.recv_match(
                type=['MISSION_COUNT', 'MISSION_ITEM', 'MISSION_ITEM_INT'],
                blocking=True,
                timeout=1.0
            )
            
            if not msg:
                continue
                
            if msg.get_type() == 'MISSION_COUNT':
                waypoint_count = msg.count
                self.get_logger().info(f'航点总数: {waypoint_count}')
                
                # 请求每个航点
                for i in range(waypoint_count):
                    self.mavlink_connection.mav.mission_request_send(
                        self.mavlink_connection.target_system,
                        self.mavlink_connection.target_component,
                        i
                    )
                    
            elif msg.get_type() in ['MISSION_ITEM', 'MISSION_ITEM_INT']:
                waypoint = self.parse_waypoint(msg)
                if waypoint:
                    self.waypoints.append(waypoint)
                    received_waypoints += 1
                    
                if waypoint_count and received_waypoints >= waypoint_count:
                    break
    
    def parse_waypoint(self, msg):
        """解析航点消息"""
        waypoint = {}
        
        if msg.get_type() == 'MISSION_ITEM_INT':
            waypoint['lat'] = msg.x / 1e7
            waypoint['lon'] = msg.y / 1e7
            waypoint['alt'] = msg.z
        else:  # MISSION_ITEM
            waypoint['lat'] = msg.x
            waypoint['lon'] = msg.y
            waypoint['alt'] = msg.z
            
        waypoint['seq'] = msg.seq
        waypoint['command'] = msg.command
        waypoint['param1'] = msg.param1
        waypoint['param2'] = msg.param2
        waypoint['param3'] = msg.param3
        waypoint['param4'] = msg.param4
        
        return waypoint
    
    def get_waypoints_callback(self, request, response):
        """获取航点列表的服务回调"""
        # 这里简化处理，实际应用中可能需要更复杂的路径规划格式
        response.plan.header = Header()
        response.plan.header.stamp = self.get_clock().now().to_msg()
        response.plan.header.frame_id = 'map'
        
        # 将航点转换为路径点
        for waypoint in self.waypoints:
            pose_stamped = self.waypoint_to_pose_stamped(waypoint)
            response.plan.poses.append(pose_stamped)
            
        return response
    
    def waypoint_to_pose_stamped(self, waypoint):
        """将航点转换为PoseStamped消息"""
        from geometry_msgs.msg import PoseStamped
        
        pose_stamped = PoseStamped()
        pose_stamped.header = Header()
        pose_stamped.header.stamp = self.get_clock().now().to_msg()
        pose_stamped.header.frame_id = 'map'
        
        # 简化处理：直接使用GPS坐标作为位置
        # 实际应用中可能需要坐标转换
        pose_stamped.pose.position.x = waypoint['lon']
        pose_stamped.pose.position.y = waypoint['lat']
        pose_stamped.pose.position.z = waypoint['alt']
        
        # 姿态设为默认值
        pose_stamped.pose.orientation.w = 1.0
        
        return pose_stamped
    
    def publish_current_waypoint(self):
        """发布当前航点"""
        if self.waypoints and self.current_waypoint_index < len(self.waypoints):
            waypoint = self.waypoints[self.current_waypoint_index]
            
            geo_point = GeoPoint()
            geo_point.latitude = waypoint['lat']
            geo_point.longitude = waypoint['lon']
            geo_point.altitude = waypoint['alt']
            
            self.waypoint_pub.publish(geo_point)
    
    def destroy_node(self):
        """节点销毁时的清理工作"""
        if self.mavlink_connection:
            self.mavlink_connection.close()
        super().destroy_node()


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    try:
        waypoint_service = WaypointService()
        rclpy.spin(waypoint_service)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'航点服务节点运行错误: {e}')
    finally:
        if 'waypoint_service' in locals():
            waypoint_service.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
