#!/usr/bin/env python3
"""
轻量级PyMAVLink到ROS2桥接节点
支持GPS、航向、速度和航点信息的转换
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy

import math
import threading
from pymavlink import mavutil

# ROS2 消息类型
from std_msgs.msg import Header
from sensor_msgs.msg import NavSatFix, NavSatStatus
from geometry_msgs.msg import PoseStamped, Quaternion, TwistStamped, Vector3
from nav_msgs.msg import Odometry
from geographic_msgs.msg import GeoPoint


class MavlinkBridge(Node):
    """MAVLink到ROS2的桥接节点"""
    
    def __init__(self):
        super().__init__('mavlink_bridge')
        
        # 声明参数
        self.declare_parameter('connection_string', '/dev/ttyUSB0')
        self.declare_parameter('baud_rate', 57600)
        self.declare_parameter('frame_id', 'base_link')
        self.declare_parameter('gps_frame_id', 'gps')
        
        # 获取参数
        self.connection_string = self.get_parameter('connection_string').value
        self.baud_rate = self.get_parameter('baud_rate').value
        self.frame_id = self.get_parameter('frame_id').value
        self.gps_frame_id = self.get_parameter('gps_frame_id').value
        
        # QoS配置
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        # 创建发布者
        self.gps_pub = self.create_publisher(NavSatFix, 'gps/fix', qos_profile)
        self.pose_pub = self.create_publisher(PoseStamped, 'pose', qos_profile)
        self.odom_pub = self.create_publisher(Odometry, 'odom', qos_profile)
        self.velocity_pub = self.create_publisher(TwistStamped, 'velocity', qos_profile)
        
        # 初始化MAVLink连接
        self.mavlink_connection = None
        self.connect_to_mavlink()
        
        # 数据存储
        self.last_gps_data = None
        self.last_attitude_data = None
        self.last_velocity_data = None
        
        # 启动MAVLink消息处理线程
        self.mavlink_thread = threading.Thread(target=self.mavlink_message_loop, daemon=True)
        self.mavlink_thread.start()
        
        self.get_logger().info(f'MAVLink桥接节点已启动，连接到: {self.connection_string}')
    
    def connect_to_mavlink(self):
        """连接到MAVLink"""
        try:
            self.mavlink_connection = mavutil.mavlink_connection(
                self.connection_string, 
                baud=self.baud_rate
            )
            self.get_logger().info('MAVLink连接成功')
        except Exception as e:
            self.get_logger().error(f'MAVLink连接失败: {e}')
            return False
        return True
    
    def mavlink_message_loop(self):
        """MAVLink消息处理循环"""
        while rclpy.ok() and self.mavlink_connection:
            try:
                msg = self.mavlink_connection.recv_match(blocking=True, timeout=1.0)
                if msg:
                    self.process_mavlink_message(msg)
            except Exception as e:
                self.get_logger().error(f'MAVLink消息处理错误: {e}')
                break
    
    def process_mavlink_message(self, msg):
        """处理MAVLink消息"""
        msg_type = msg.get_type()
        
        if msg_type == 'GPS_RAW_INT':
            self.handle_gps_raw_int(msg)
        elif msg_type == 'GLOBAL_POSITION_INT':
            self.handle_global_position_int(msg)
        elif msg_type == 'ATTITUDE':
            self.handle_attitude(msg)
        elif msg_type == 'VFR_HUD':
            self.handle_vfr_hud(msg)
        elif msg_type == 'LOCAL_POSITION_NED':
            self.handle_local_position_ned(msg)
    
    def handle_gps_raw_int(self, msg):
        """处理GPS_RAW_INT消息"""
        if msg.fix_type < 2:  # 无GPS定位
            return
            
        nav_sat_fix = NavSatFix()
        nav_sat_fix.header = self.create_header(self.gps_frame_id)
        
        # GPS状态
        nav_sat_fix.status.status = NavSatStatus.STATUS_FIX if msg.fix_type >= 3 else NavSatStatus.STATUS_NO_FIX
        nav_sat_fix.status.service = NavSatStatus.SERVICE_GPS
        
        # 位置信息 (MAVLink使用1e7缩放)
        nav_sat_fix.latitude = msg.lat / 1e7
        nav_sat_fix.longitude = msg.lon / 1e7
        nav_sat_fix.altitude = msg.alt / 1000.0  # mm转m
        
        # 协方差 (简化处理)
        nav_sat_fix.position_covariance_type = NavSatFix.COVARIANCE_TYPE_APPROXIMATED
        nav_sat_fix.position_covariance = [0.0] * 9
        if msg.eph != 65535:  # 有效的水平精度
            hdop = msg.eph / 100.0
            nav_sat_fix.position_covariance[0] = hdop * hdop  # 经度方差
            nav_sat_fix.position_covariance[4] = hdop * hdop  # 纬度方差
        if msg.epv != 65535:  # 有效的垂直精度
            vdop = msg.epv / 100.0
            nav_sat_fix.position_covariance[8] = vdop * vdop  # 高度方差
        
        self.gps_pub.publish(nav_sat_fix)
        self.last_gps_data = nav_sat_fix

    def handle_global_position_int(self, msg):
        """处理GLOBAL_POSITION_INT消息"""
        nav_sat_fix = NavSatFix()
        nav_sat_fix.header = self.create_header(self.gps_frame_id)

        nav_sat_fix.status.status = NavSatStatus.STATUS_FIX
        nav_sat_fix.status.service = NavSatStatus.SERVICE_GPS

        nav_sat_fix.latitude = msg.lat / 1e7
        nav_sat_fix.longitude = msg.lon / 1e7
        nav_sat_fix.altitude = msg.alt / 1000.0

        nav_sat_fix.position_covariance_type = NavSatFix.COVARIANCE_TYPE_UNKNOWN
        nav_sat_fix.position_covariance = [0.0] * 9

        self.gps_pub.publish(nav_sat_fix)
        self.last_gps_data = nav_sat_fix

    def handle_attitude(self, msg):
        """处理ATTITUDE消息 - 转换为姿态信息"""
        pose_stamped = PoseStamped()
        pose_stamped.header = self.create_header(self.frame_id)

        # 将欧拉角转换为四元数
        quaternion = self.euler_to_quaternion(msg.roll, msg.pitch, msg.yaw)
        pose_stamped.pose.orientation = quaternion

        # 如果有GPS数据，添加位置信息
        if self.last_gps_data:
            pose_stamped.pose.position.x = 0.0  # 相对位置，可根据需要修改
            pose_stamped.pose.position.y = 0.0
            pose_stamped.pose.position.z = 0.0

        self.pose_pub.publish(pose_stamped)
        self.last_attitude_data = msg

        # 发布里程计信息
        self.publish_odometry()

    def handle_vfr_hud(self, msg):
        """处理VFR_HUD消息 - 速度和航向信息"""
        twist_stamped = TwistStamped()
        twist_stamped.header = self.create_header(self.frame_id)

        # 线速度 (地速)
        twist_stamped.twist.linear.x = msg.groundspeed  # 前向速度
        twist_stamped.twist.linear.y = 0.0
        twist_stamped.twist.linear.z = -msg.climb  # 爬升率 (NED坐标系)

        # 角速度 (如果有ATTITUDE数据)
        if self.last_attitude_data:
            twist_stamped.twist.angular.x = self.last_attitude_data.rollspeed
            twist_stamped.twist.angular.y = self.last_attitude_data.pitchspeed
            twist_stamped.twist.angular.z = self.last_attitude_data.yawspeed

        self.velocity_pub.publish(twist_stamped)
        self.last_velocity_data = twist_stamped

    def handle_local_position_ned(self, msg):
        """处理LOCAL_POSITION_NED消息"""
        twist_stamped = TwistStamped()
        twist_stamped.header = self.create_header(self.frame_id)

        # NED坐标系的速度
        twist_stamped.twist.linear.x = msg.vx  # 北向速度
        twist_stamped.twist.linear.y = msg.vy  # 东向速度
        twist_stamped.twist.linear.z = msg.vz  # 下向速度

        self.velocity_pub.publish(twist_stamped)
        self.last_velocity_data = twist_stamped

    def publish_odometry(self):
        """发布里程计信息"""
        if not (self.last_gps_data and self.last_attitude_data):
            return

        odom = Odometry()
        odom.header = self.create_header(self.frame_id)
        odom.child_frame_id = self.frame_id

        # 位置 (简化处理，实际应用中可能需要坐标转换)
        odom.pose.pose.position.x = 0.0
        odom.pose.pose.position.y = 0.0
        odom.pose.pose.position.z = 0.0

        # 姿态
        quaternion = self.euler_to_quaternion(
            self.last_attitude_data.roll,
            self.last_attitude_data.pitch,
            self.last_attitude_data.yaw
        )
        odom.pose.pose.orientation = quaternion

        # 速度
        if self.last_velocity_data:
            odom.twist.twist = self.last_velocity_data.twist

        # 协方差 (简化处理)
        odom.pose.covariance = [0.1] * 36
        odom.twist.covariance = [0.1] * 36

        self.odom_pub.publish(odom)

    def euler_to_quaternion(self, roll, pitch, yaw):
        """欧拉角转四元数"""
        cy = math.cos(yaw * 0.5)
        sy = math.sin(yaw * 0.5)
        cp = math.cos(pitch * 0.5)
        sp = math.sin(pitch * 0.5)
        cr = math.cos(roll * 0.5)
        sr = math.sin(roll * 0.5)

        quaternion = Quaternion()
        quaternion.w = cy * cp * cr + sy * sp * sr
        quaternion.x = cy * cp * sr - sy * sp * cr
        quaternion.y = sy * cp * sr + cy * sp * cr
        quaternion.z = sy * cp * cr - cy * sp * sr

        return quaternion

    def create_header(self, frame_id):
        """创建ROS消息头"""
        header = Header()
        header.stamp = self.get_clock().now().to_msg()
        header.frame_id = frame_id
        return header

    def request_waypoints(self):
        """请求航点信息"""
        if not self.mavlink_connection:
            return

        try:
            # 请求任务列表
            self.mavlink_connection.mav.mission_request_list_send(
                self.mavlink_connection.target_system,
                self.mavlink_connection.target_component
            )
            self.get_logger().info('已请求航点列表')
        except Exception as e:
            self.get_logger().error(f'请求航点失败: {e}')

    def get_current_waypoint(self):
        """获取当前航点"""
        if not self.mavlink_connection:
            return None

        try:
            # 请求当前任务项
            self.mavlink_connection.mav.mission_request_send(
                self.mavlink_connection.target_system,
                self.mavlink_connection.target_component,
                0  # 序列号
            )
        except Exception as e:
            self.get_logger().error(f'获取当前航点失败: {e}')
            return None

    def destroy_node(self):
        """节点销毁时的清理工作"""
        if self.mavlink_connection:
            self.mavlink_connection.close()
        super().destroy_node()


def main(args=None):
    """主函数"""
    rclpy.init(args=args)

    try:
        mavlink_bridge = MavlinkBridge()
        rclpy.spin(mavlink_bridge)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'节点运行错误: {e}')
    finally:
        if 'mavlink_bridge' in locals():
            mavlink_bridge.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
