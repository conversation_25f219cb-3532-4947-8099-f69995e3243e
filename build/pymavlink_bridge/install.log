/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge/mavlink_bridge.py
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge/__init__.py
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge/waypoint_service.py
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge/__pycache__/mavlink_bridge.cpython-310.pyc
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge/__pycache__/__init__.cpython-310.pyc
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge/__pycache__/waypoint_service.cpython-310.pyc
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/ament_index/resource_index/packages/pymavlink_bridge
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/package.xml
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/launch/mavlink_bridge.launch.py
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge-0.0.0-py3.10.egg-info/SOURCES.txt
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge-0.0.0-py3.10.egg-info/zip-safe
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge-0.0.0-py3.10.egg-info/PKG-INFO
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge-0.0.0-py3.10.egg-info/top_level.txt
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge-0.0.0-py3.10.egg-info/dependency_links.txt
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge-0.0.0-py3.10.egg-info/entry_points.txt
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages/pymavlink_bridge-0.0.0-py3.10.egg-info/requires.txt
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/pymavlink_bridge/mavlink_bridge
/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/pymavlink_bridge/waypoint_service
