[0.099s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'pymavlink_bridge']
[0.099s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['pymavlink_bridge'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7fcc439a8a00>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fcc439a8400>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fcc439a8400>>, mixin_verb=('build',))
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.128s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/pymavlink_ros2'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.129s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.129s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.129s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.129s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.129s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.129s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extension 'ignore'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extension 'ignore_ament_install'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extensions ['colcon_pkg']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extension 'colcon_pkg'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extensions ['colcon_meta']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extension 'colcon_meta'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extensions ['ros']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/pymavlink_bridge) by extension 'ros'
[0.140s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pymavlink_bridge' with type 'ros.ament_python' and name 'pymavlink_bridge'
[0.140s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.140s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.140s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.140s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.140s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.155s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.155s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.157s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 274 installed packages in /opt/ros/humble
[0.159s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 391 installed packages in /opt/ros/iron
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'cmake_args' from command line to 'None'
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'cmake_target' from command line to 'None'
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.215s] Level 5:colcon.colcon_core.verb:set package 'pymavlink_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.215s] DEBUG:colcon.colcon_core.verb:Building package 'pymavlink_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/pymavlink_ros2/build/pymavlink_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge', 'merge_install': False, 'path': '/home/<USER>/pymavlink_ros2/src/pymavlink_bridge', 'symlink_install': False, 'test_result_base': None}
[0.215s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.216s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.216s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/pymavlink_ros2/src/pymavlink_bridge' with build type 'ament_python'
[0.216s] Level 1:colcon.colcon_core.shell:create_environment_hook('pymavlink_bridge', 'ament_prefix_path')
[0.218s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.218s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/hook/ament_prefix_path.ps1'
[0.219s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/hook/ament_prefix_path.dsv'
[0.219s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/hook/ament_prefix_path.sh'
[0.220s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.220s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.356s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/pymavlink_ros2/src/pymavlink_bridge'
[0.356s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.356s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.830s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/pymavlink_ros2/src/pymavlink_bridge': PYTHONPATH=/home/<USER>/pymavlink_ros2/build/pymavlink_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated setup.py egg_info --egg-base ../../build/pymavlink_bridge build --build-base /home/<USER>/pymavlink_ros2/build/pymavlink_bridge/build install --record /home/<USER>/pymavlink_ros2/build/pymavlink_bridge/install.log --single-version-externally-managed install_data
[1.158s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/pymavlink_ros2/src/pymavlink_bridge' returned '0': PYTHONPATH=/home/<USER>/pymavlink_ros2/build/pymavlink_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated setup.py egg_info --egg-base ../../build/pymavlink_bridge build --build-base /home/<USER>/pymavlink_ros2/build/pymavlink_bridge/build install --record /home/<USER>/pymavlink_ros2/build/pymavlink_bridge/install.log --single-version-externally-managed install_data
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge' for CMake module files
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge' for CMake config files
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib'
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/bin'
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/pkgconfig/pymavlink_bridge.pc'
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/lib/python3.10/site-packages'
[1.167s] Level 1:colcon.colcon_core.shell:create_environment_hook('pymavlink_bridge', 'pythonpath')
[1.168s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/hook/pythonpath.ps1'
[1.168s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/hook/pythonpath.dsv'
[1.168s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/hook/pythonpath.sh'
[1.168s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/bin'
[1.168s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pymavlink_bridge)
[1.168s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/package.ps1'
[1.170s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/package.dsv'
[1.171s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/package.sh'
[1.172s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/package.bash'
[1.174s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/pymavlink_bridge/package.zsh'
[1.175s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/pymavlink_ros2/install/pymavlink_bridge/share/colcon-core/packages/pymavlink_bridge)
[1.175s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.175s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.175s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.175s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.181s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.182s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.182s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.197s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.198s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/pymavlink_ros2/install/local_setup.ps1'
[1.199s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/pymavlink_ros2/install/_local_setup_util_ps1.py'
[1.201s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/pymavlink_ros2/install/setup.ps1'
[1.203s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/pymavlink_ros2/install/local_setup.sh'
[1.205s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/pymavlink_ros2/install/_local_setup_util_sh.py'
[1.205s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/pymavlink_ros2/install/setup.sh'
[1.207s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/pymavlink_ros2/install/local_setup.bash'
[1.208s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/pymavlink_ros2/install/setup.bash'
[1.210s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/pymavlink_ros2/install/local_setup.zsh'
[1.211s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/pymavlink_ros2/install/setup.zsh'
